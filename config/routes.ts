export default [
  {
    path: '/user',
    layout: false,
    routes: [
      { path: '/user/login', layout: false, name: '登录', component: './user/login' },
      { path: '/user', redirect: '/user/login' },
      {
        name: '注册结果',
        icon: 'smile',
        path: '/user/register-result',
        component: './user/register-result',
      },
      { name: '注册', icon: 'smile', path: '/user/register', component: './user/register' },
      // { component: '404', path: '/*' },
    ],
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    icon: 'dashboard',
    component: './dashboard/monitor',
  },
  {
    name: '文章管理',
    icon: 'user',
    path: '/article',
    routes: [
      { path: '/article', redirect: '/article/addArticle' },
      { name: '文章任务', icon: 'smile', path: '/article/addArticle', component: './article/addArticle' },
      { name: '文章管理', icon: 'smile', path: '/article/articleList', component: './article/articleList' },
      { name: '文章上传', icon: 'smile', path: '/article/uploadArticle', component: './article/uploadArticle' },
    ],
  },
  {
    name: '编辑器',
    icon: 'edit',
    path: '/edit',
    component: "./edit"
  },
  {
    name: '编辑器',
    icon: 'edit',
    path: '/edit/:id',
    component: "./edit/components/edit",
    hideInMenu: true,
    menuRender:false,
    target: '_blank',
  },
  {
    name: '设置页',
    icon: 'setting',
    path: '/account',
    routes: [
      {
        name: 'POE生成文章设置',
        icon: 'smile',
        path: '/account/poeSettings',
        component: './account/poeSettings',
      },
      {
        name: 'API生成文章设置',
        icon: 'smile',
        path: '/account/apiSetting',
        component: './account/apiSetting',
      },
      {
        name: 'Promote设置',
        icon: 'smile',
        path: '/account/promoteSetting',
        component: './account/promoteSetting',
      },
      {
        name: '公众号设置',
        icon: 'smile',
        path: '/account/accountSetting',
        component: './account/accountSetting',
      },
      {
        name: '分类管理',
        icon: 'appstore',
        path: '/account/imageClassify',
        component: './account/imageClassify',
      },
    ],
  },
  { path: '/', redirect: '/dashboard/monitor' },
  { component: '404', path: '/*' },
];
