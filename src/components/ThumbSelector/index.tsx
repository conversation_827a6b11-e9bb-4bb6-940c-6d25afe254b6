import React, { useState, useEffect } from 'react';
import { Radio, Upload, message, Row, Col, Card, Spin, Empty, Cascader } from 'antd';
import { InboxOutlined } from '@ant-design/icons';
import { imageGetArticleThumbs, imageAddThumbImage } from '@/services/swagger/image';
import { categoryGetAllArticleClassifications } from "@/services/swagger/category";

// 定义分类选项类型/
interface ClassifyOption {
  label: string;
  value: string;
  children?: ClassifyOption[];
}

// 定义缩略图类型
export interface ThumbItem {
  id?: string;
  thumb_media_id: string;
  url?: string | null;
  title?: string | null;
  local_path?: string | null;
  official_account_id?: string;
}

// 组件属性/
interface ThumbSelectorProps {
  value?: string;  // 当前选中的缩略图ID
  onChange?: (value: string) => void;  // 选择变更回调
  categoryId?: string;  // 分类ID
  officialAccountId?:string;
}

/**
 * 缩略图选择器组件
 * 提供图片选择和上传功能
 */
const ThumbSelector: React.FC<ThumbSelectorProps> = ({
  value,
  onChange,
  officialAccountId,
  categoryId
}) => {
  const [thumbMode, setThumbMode] = useState<'select' | 'upload'>('select');
  const [thumbList, setThumbList] = useState<ThumbItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedThumb, setSelectedThumb] = useState<string | undefined>(value);
  const [classifyOptions, setClassifyOptions] = useState<ClassifyOption[]>([]);
  const [selectedClassify, setSelectedClassify] = useState<string[]>([]);
  const [filterCategoryId, setFilterCategoryId] = useState<string | undefined>(categoryId);

  // 获取分类列表
  const fetchClassifyOptions = async () => {
    try {
      const res = await categoryGetAllArticleClassifications({ parent_only: false });
      if (res.succeed && res.data) {
        const parentMap = new Map();

        // 创建父级分类映射
        res.data.forEach((item: API.ArticleCategoryPublic) => {
          if (!item.parent) {
            parentMap.set(item.id, {
              label: item.name,
              value: item.id || '',
              children: []
            });
          }
        });

        // 添加子分类到对应的父分类
        res.data.forEach((item: API.ArticleCategoryPublic) => {
          if (item.parent && item.parent.id && parentMap.has(item.parent.id)) {
            parentMap.get(item.parent.id).children.push({
              label: item.name,
              value: item.id || ''
            });
          }
        });

        setClassifyOptions(Array.from(parentMap.values()));
      }
    } catch (error) {
      console.error('获取分类列表失败:', error);
    }
  };
  // 获取缩略图列//表
  const fetchThumbList = async (category_id?: string) => {
    try {
      setLoading(true);
      const params: any = { current: 1, pageSize: 50 };
      if (category_id) {
        params.category_id = category_id;
      }

      const res = await imageGetArticleThumbs(params);
      if (res.succeed && res.data?.items) {
        setThumbList(res.data.items as ThumbItem[]);
      } else {
        message.error('获取缩略图列表失败');
      }
    } catch (error) {
      console.error('获取缩略图列表失败:', error);
      message.error('获取缩略图列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    fetchClassifyOptions();
  }, []);

  // 分类或筛选变更时刷新
  useEffect(() => {
    fetchThumbList(filterCategoryId);
  }, [filterCategoryId]);

  // 当外部value变更时，更新内部状态
  useEffect(() => {
    if (value !== selectedThumb) {
      setSelectedThumb(value);
    }
  }, [value]);

  // 当外部分类ID变更时，更新内部筛选
  useEffect(() => {
    if (categoryId) {
      setFilterCategoryId(categoryId);

      // 查找完整分类路径并设置
      const findCategoryPath = (options: ClassifyOption[], categoryId: string, currentPath: string[] = []): string[] | null => {
        for (const option of options) {
          // 检查当前选项
          if (option.value === categoryId) {
            return [...currentPath, option.value];
          }

          // 检查子选项
          if (option.children && option.children.length > 0) {
            const found = findCategoryPath(option.children, categoryId, [...currentPath, option.value]);
            if (found) return found;
          }
        }
        return null;
      };

      if (classifyOptions.length > 0) {
        const path = findCategoryPath(classifyOptions, categoryId);
        if (path) {
          setSelectedClassify(path);
        }
      }
    }
  }, [categoryId, classifyOptions]);

  // 选择缩略图
  const handleThumbSelect = (thumbId: string) => {
    setSelectedThumb(thumbId);
    onChange?.(thumbId);
  };

  // 缩略图模式切换
  const handleThumbModeChange = (mode: 'select' | 'upload') => {
    setThumbMode(mode);
  };

  // 处理分类选择变化
  const handleClassifyChange = (value: string[]) => {
    setSelectedClassify(value);

    if (value && value.length > 0) {
      // 使用最后一个值作为实际分类ID
      setFilterCategoryId(value[value.length - 1]);
    } else {
      setFilterCategoryId(undefined);
    }
  };

  // 处理缩略图上传
  const handleThumbUpload = async (options: any) => {
    const { file, onSuccess, onError } = options;

    try {
      // 检查是否有选择公众号
      if (!officialAccountId) {
        message.error('请先选择公众号');
        onError(new Error('请先选择公众号'));
        return;
      }

      // 构建FormData对象
      const formData = new FormData();
      formData.append('media', file);

      // 请求微信API上传素材
      const response = await fetch(`https://wxcomponent-290797-160013-8-**********.sh.run.tcloudbase.com/wxcomponent/official-account/material/uploadArticleThumb?appid=${officialAccountId}`, {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();
      console.log(result);

      if (result.media_id) {
        // 上传成功，调用后端API保存记录
        const thumbData: {
          thumb_media_id: string;
          url?: string | null;
          title?: string | null;
          category_id?: string | null;
          official_account_id: string;
        } = {
          thumb_media_id: result.media_id,
          url: result.url,
          title: file.name,
          // 使用选择的分类ID或传入的分类ID
          category_id: selectedClassify.length > 0 ? selectedClassify[selectedClassify.length - 1] : filterCategoryId,
          // 确保公众号ID存在
          official_account_id: officialAccountId || ''
        };

        const saveResult = await imageAddThumbImage(thumbData);

        if (saveResult.succeed) {
          message.success('上传缩略图成功');

          // 更新选中状态
          const newThumbId = result.media_id;
          setSelectedThumb(newThumbId);
          onChange?.(newThumbId);

          // 刷新缩略图列表
          fetchThumbList(filterCategoryId);

          onSuccess(result);
        } else {
          message.error('保存缩略图记录失败');
          onError(new Error('保存缩略图记录失败'));
        }
      } else {
        message.error('上传缩略图失败: ' + (result.errmsg || '未知错误'));
        onError(new Error('上传缩略图失败'));
      }
    } catch (error) {
      console.error('上传缩略图出错:', error);
      message.error('上传缩略图失败');
      onError(error);
    }
  };

  return (
    <div className="thumb-selector">
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <Radio.Group
          value={thumbMode}
          onChange={(e) => handleThumbModeChange(e.target.value)}
        >
          <Radio.Button value="select">从现有图片选择</Radio.Button>
          <Radio.Button value="upload">上传新图片</Radio.Button>
        </Radio.Group>

        <Cascader
          options={classifyOptions}
          value={selectedClassify}
          onChange={(value) => handleClassifyChange(value as string[])}
          placeholder="按分类筛选"
          style={{ width: '260px' }}
          allowClear
        />
      </div>

      {thumbMode === 'select' ? (
        <div className="thumb-gallery" style={{ marginTop: 16, minHeight: 200 }}>
          <Spin spinning={loading}>
            {thumbList.length > 0 ? (
              <Row gutter={[16, 16]}>
                {thumbList.map(thumb => (
                  <Col span={6} key={thumb.thumb_media_id}>
                    <Card
                      hoverable
                      cover={thumb.url ? (
                        <div style={{ height: 120, overflow: 'hidden', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                          <img
                            src={"https://images.weserv.nl/?url="+thumb.url}
                            alt={thumb.title || '缩略图'}
                            style={{ maxWidth: '100%', maxHeight: '100%', objectFit: 'cover' }}
                          />
                        </div>
                      ) : (
                        <div style={{ height: 120, background: '#f0f0f0', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                          无图片
                        </div>
                      )}
                      onClick={() => handleThumbSelect(thumb.thumb_media_id)}
                      className={selectedThumb === thumb.thumb_media_id ? 'selected-thumb' : ''}
                      style={{
                        border: selectedThumb === thumb.thumb_media_id ? '2px solid #1890ff' : '1px solid #f0f0f0',
                        cursor: 'pointer',
                        transform: selectedThumb === thumb.thumb_media_id ? 'translateY(-5px)' : 'none',
                        boxShadow: selectedThumb === thumb.thumb_media_id ? '0 4px 12px rgba(0,0,0,0.15)' : 'none'
                      }}
                    >
                      <Card.Meta title={thumb.title || thumb.thumb_media_id} />
                    </Card>
                  </Col>
                ))}
              </Row>
            ) : (
              <Empty description="暂无缩略图" />
            )}
          </Spin>
        </div>
      ) : (
        <div>
          <div style={{ marginBottom: 16 }}>
            <Cascader
              options={classifyOptions}
              value={selectedClassify}
              onChange={(value) => handleClassifyChange(value as string[])}
              placeholder="选择要上传到的分类"
              style={{ width: '100%' }}
              allowClear
            />
          </div>
          <Upload.Dragger
            name="file"
            customRequest={handleThumbUpload}
            showUploadList={true}
            accept="image/*"
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p className="ant-upload-hint">支持单个或批量上传</p>
          </Upload.Dragger>
        </div>
      )}
    </div>
  );
};

export default ThumbSelector;
