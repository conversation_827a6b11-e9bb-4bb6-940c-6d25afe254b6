import React, {useRef, useState} from 'react';
import {PlusOutlined, DeleteOutlined, EditOutlined} from '@ant-design/icons';
import {
  Button,
  Space,
  message,
  Popconfirm,
  Input,
  Form,
  Modal,
  Card,
  Table,
  Select,
  Typography,
  Badge,
} from 'antd';
import type {FormInstance} from 'antd';
import {PageContainer} from '@ant-design/pro-components';
import {
  categoryCreateArticleCategory,
  categoryDeleteArticleCategory,
  categoryGetAllArticleClassifications,
  categoryUpdateArticleCategory,
} from '@/services/swagger/category';

const {Text} = Typography;
const {Option} = Select;


// 主组件
const ImageClassifyPage: React.FC = () => {
  // 状态
  const [loading, setLoading] = useState<boolean>(false);
  const [classifyList, setClassifyList] = useState<API.ArticleCategoryPublic[]>([]);
  const [parentList, setParentList] = useState<API.ArticleCategoryPublic[]>([]);
  const [editItem, setEditItem] = useState<API.ArticleCategoryPublic | null>(null);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [modalTitle, setModalTitle] = useState<string>('');
  const [refreshTrigger, setRefreshTrigger] = useState<number>(0);

  // 表单引用
  const formRef = useRef<FormInstance>(null);

  // 当组件挂载或需要刷新时获取数据
  React.useEffect(() => {
    fetchClassifyList();
  }, [refreshTrigger]);

  // 获取分类列表数据
  const fetchClassifyList = async () => {
    setLoading(true);
    try {
      const res = await categoryGetAllArticleClassifications({parent_only: false});
      if (res.succeed && res.data) {
        setClassifyList(res.data);
        // 过滤出父级分类
        const parents = res.data.filter(item => !item.parent);
        setParentList(parents);
      } else {
        message.error('获取分类列表失败');
      }
    } catch (error) {
      console.error('获取分类列表出错:', error);
      message.error('获取分类列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 显示添加模态框
  const showAddModal = () => {
    setModalTitle('添加图片分类');
    setEditItem(null);
    setModalVisible(true);
    // 表单重置
    setTimeout(() => {
      formRef.current?.resetFields();
    }, 0);
  };

  // 显示编辑模态框
  const showEditModal = (record: API.ArticleCategoryPublic) => {
    setModalTitle('编辑分类');
    setEditItem(record);
    setModalVisible(true);
    // 设置表单初始值
    setTimeout(() => {
      formRef.current?.setFieldsValue({
        name: record.name,
        description: record.description,
        parent_id: record.parent?.id,
      });
    }, 0);
  };

  // 关闭模态框
  const handleCancel = () => {
    setModalVisible(false);
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await formRef.current?.validateFields();

      if (editItem) {
        // 更新分类
        const res = await categoryUpdateArticleCategory(
          {category_id: editItem.id || ''},
          values
        );
        if (res.succeed) {
          message.success('更新分类成功');
          setModalVisible(false);
          setRefreshTrigger(prev => prev + 1);
        } else {
          message.error('更新分类失败: ' + (res.errorMessage || '未知错误'));
        }
      } else {
        // 创建分类
        const res = await categoryCreateArticleCategory(values);
        if (res.succeed) {
          message.success('创建分类成功');
          setModalVisible(false);
          setRefreshTrigger(prev => prev + 1);
        } else {
          message.error('创建分类失败: ' + (res.errorMessage || '未知错误'));
        }
      }
    } catch (error) {
      console.error('表单提交出错:', error);
    }
  };

  // 删除分类
  const handleDelete = async (id: string) => {
    try {
      const res = await categoryDeleteArticleCategory({category_id: id});
      if (res.succeed) {
        message.success('删除分类成功');
        setRefreshTrigger(prev => prev + 1);
      } else {
        message.error('删除分类失败: ' + (res.errorMessage || '未知错误'));
      }
    } catch (error) {
      console.error('删除分类出错:', error);
      message.error('删除分类失败');
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '分类名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: API.ArticleCategoryPublic) => (
        <Space>
          {record.parent ? (
            <Text>{text}</Text>
          ) : (
            <Text strong>{text}</Text>
          )}
          {!record.parent && <Badge color="blue" count="主分类"/>}
        </Space>
      ),
    },
    {
      title: '上级分类',
      dataIndex: 'parent',
      key: 'parent',
      render: (parent: API.ArticleCategoryPublic | null) => parent?.name || '无',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      render: (text: string) => text || '无',
    },
    {
      title: '创建时间',
      dataIndex: 'create_at',
      key: 'create_at',
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: API.ArticleCategoryPublic) => (
        <Space>
          <Button
            type="text"
            icon={<EditOutlined/>}
            onClick={() => showEditModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除该分类吗?"
            onConfirm={() => handleDelete(record.id || '')}
            okText="确定"
            cancelText="取消"
          >
            <Button type="text" danger icon={<DeleteOutlined/>}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <PageContainer>
      <Card>
        <Space direction="vertical" style={{width: '100%'}}>
          <Space style={{marginBottom: 16}}>
            <Button
              type="primary"
              icon={<PlusOutlined/>}
              onClick={showAddModal}
            >
              添加分类
            </Button>
          </Space>

          <Table
            columns={columns}
            dataSource={classifyList}
            rowKey="id"
            loading={loading}
            pagination={{pageSize: 10}}
          />
        </Space>
      </Card>

      {/* 添加/编辑分类模态框 */}
      <Modal
        title={modalTitle}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={handleCancel}
        maskClosable={false}
        destroyOnClose
      >
        <Form
          ref={formRef}
          layout="vertical"
          initialValues={{parent_id: undefined}}
        >
          <Form.Item
            name="name"
            label="分类名称"
            rules={[{required: true, message: '请输入分类名称'}]}
          >
            <Input placeholder="请输入分类名称" maxLength={50}/>
          </Form.Item>

          <Form.Item
            name="parent_id"
            label="上级分类"
            extra="不选择则创建为一级分类"
          >
            <Select
              placeholder="请选择上级分类"
              allowClear
              style={{width: '100%'}}
            >
              {parentList.map(item => (
                <Option key={item.id} value={item.id}>
                  {item.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item name="description" label="分类描述">
            <Input.TextArea
              placeholder="请输入分类描述"
              maxLength={200}
              rows={4}
            />
          </Form.Item>
        </Form>
      </Modal>
    </PageContainer>
  );
};

export default ImageClassifyPage;
