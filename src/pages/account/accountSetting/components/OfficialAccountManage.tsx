import { PlusOutlined } from '@ant-design/icons';
import { ProTable, ProColumns, ActionType } from '@ant-design/pro-components';
import {
  Button,
  message,
  Modal,
  Tag,
  Form,
  Input,
  Typography,
  Popconfirm,
  Steps,
  QRCode,
  Cascader,
} from 'antd';
import React, { useState, useRef, useEffect } from 'react';
import {
  officialAccountGetOfficialAccountList,
  officialAccountAddOfficialAccount,
  officialAccountDeleteOfficialAccount,
  officialAccountConfirmOfficialAccount,
} from '@/services/swagger/officialAccount';
import { categoryGetAllArticleClassifications } from '@/services/swagger/category';

const { Link } = Typography;
const { Step } = Steps;

const OfficialAccountManage: React.FC = () => {
  const [form] = Form.useForm();
  const [modalVisible, setModalVisible] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [appid, setAppid] = useState<string>('');
  const actionRef = useRef<ActionType>();

  const columns: ProColumns<API.OfficialAccountPublic>[] = [
    {
      title: '公众号ID',
      dataIndex: 'appid',
      key: 'appid',
      copyable: true,
      ellipsis: true,
    },
    {
      title: '公众号名称',
      dataIndex: 'nick_name',
      key: 'nick_name',
      ellipsis: true,
    },
    {
      title: '主体名称',
      dataIndex: 'principal_name',
      key: 'principal_name',
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      search: false,
      render: (_, record) => (
        <Tag color={record.is_active ? 'success' : 'error'}>
          {record.is_active ? '正常' : '异常'}
        </Tag>
      ),
    },
    {
      title: '授权时间',
      dataIndex: 'auth_time',
      key: 'auth_time',
      valueType: 'dateTime',
      sorter: true,
      hideInSearch: true,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      render: (_, record) => [
        <Popconfirm
          key="delete"
          title="删除公众号"
          description="确定要删除该公众号吗？"
          okText="确认"
          cancelText="取消"
          onConfirm={async () => {
            if (record.appid) {
              try {
                const res = await officialAccountDeleteOfficialAccount({
                  appid: record.appid,
                });
                if (res.succeed) {
                  message.success('删除成功');
                  actionRef.current?.reload();
                } else {
                  message.error(res.errorMessage || '删除失败');
                }
              } catch (error) {
                message.error('操作失败，请重试');
              }
            }
          }}
        >
          <Button type="primary" danger>
            删除
          </Button>
        </Popconfirm>,
      ],
    },
  ];

  const handleAddOfficialAccount = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      const res = await officialAccountAddOfficialAccount({
        appid: values.appid,
      });

      if (res.succeed) {
        setAppid(values.appid);
        setCurrentStep(1);
        message.success('初始化成功，请继续下一步');
      } else {
        message.error(res.errorMessage || '操作失败');
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleConfirmAuthorization = async () => {
    try {
      setLoading(true);
      const res = await officialAccountConfirmOfficialAccount({
        appid,
      });

      if (res.succeed) {
        message.success('授权确认成功');
        setModalVisible(false);
        setCurrentStep(0);
        form.resetFields();
        actionRef.current?.reload();
      } else {
        message.error(res.errorMessage || '确认授权失败');
      }
    } catch (error) {
      message.error('操作失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setModalVisible(false);
    setCurrentStep(0);
    form.resetFields();
  };

  return (
    <>
      <ProTable<API.OfficialAccountPublic>
        headerTitle="公众号账户列表"
        actionRef={actionRef}
        rowKey="id"
        search={false}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            onClick={() => setModalVisible(true)}
            icon={<PlusOutlined />}
          >
            新增公众号
          </Button>,
        ]}
        request={async (params) => {
          const res = await officialAccountGetOfficialAccountList({
            ...params,
          });
          return {
            data: res.data || [],
            success: res.succeed,
          };
        }}
        columns={columns}
      />

      <Modal
        title="添加公众号账户"
        open={modalVisible}
        onCancel={handleCancel}
        footer={null}
        destroyOnClose
      >
        <Steps current={currentStep} style={{ marginBottom: 24 }}>
          <Step title="输入AppID" />
          <Step title="微信授权" />
          <Step title="确认授权" />
        </Steps>

        {currentStep === 0 && (
          <Form form={form} layout="vertical">
            <Form.Item
              name="appid"
              label="公众号AppID"
              rules={[{ required: true, message: '请输入公众号AppID' }]}
            >
              <Input placeholder="请输入公众号AppID" />
            </Form.Item>
            <Form.Item>
              <Button type="primary" onClick={handleAddOfficialAccount} loading={loading}>
                提交
              </Button>
            </Form.Item>
          </Form>
        )}

        {currentStep === 1 && (
          <div style={{ textAlign: 'center' }}>
            <p>请使用公众号管理员微信扫码进行鉴权</p>
            <QRCode style={{alignItems:"center"}} value="https://wxcomponent-290797-160013-8-**********.sh.run.tcloudbase.com/#/authorizeH5" />
            <Link href="https://wxcomponent-290797-160013-8-**********.sh.run.tcloudbase.com/#/authorize" target="_blank">
              点击这里进行授权
            </Link>
            <br />
            <br />
            <Button type="primary" onClick={() => setCurrentStep(2)}>
              我已完成授权
            </Button>
          </div>
        )}

        {currentStep === 2 && (
          <div style={{ textAlign: 'center' }}>
            <p>点击确认按钮完成授权</p>
            <Button type="primary" onClick={handleConfirmAuthorization} loading={loading}>
              确认授权
            </Button>
          </div>
        )}
      </Modal>
    </>
  );
};

export default OfficialAccountManage;
