import {
  SelectProps,
  Tag,
  Popconfirm,
  message,
  Button,
  Tooltip,
  Cascader,
  Modal,
  Form,
  Input,
  Checkbox,
  Space,
  Select,
  InputNumber
} from 'antd';
import React, {useState, useEffect} from 'react';
import {ProColumns, ProFormSelect, ProTable} from "@ant-design/pro-components";
import {
  articleDeletePoeArticle,
  articleGetArticlesSummary,
  articleSycnArticlesToDraft
} from "@/services/swagger/article";
import {
  poeGetPoeChatSessions,
  poeGetPoeClientList
} from "@/services/swagger/poe";
import {
  thirdApiKeyGetModels,
  thirdApiKeyGetThirdApiKeyPlatform
} from "@/services/swagger/thirdApiKey";
import {categoryGetAllArticleClassifications} from "@/services/swagger/category";
import {
  DeleteOutlined, EditOutlined,
  FileImageOutlined,
  FileTextOutlined,
  OrderedListOutlined,
  RadiusSettingOutlined,
  SyncOutlined,
  TagsOutlined,
} from "@ant-design/icons";
import {officialAccountGetOfficialAccountList} from "@/services/swagger/officialAccount";
import {imageGetArticleThumbs} from '@/services/swagger/image';
import ThumbSelector from '@/components/ThumbSelector';

// @ts-ignore
import {Link} from '@umijs/max';

// 定义分类选项类型
interface ClassifyOption {
  label: string;
  value: string;
  children?: ClassifyOption[];
}

// 缩略图类型
interface ThumbItem {
  id?: string;
  thumb_media_id: string;
  url?: string | null;
  title?: string | null;
}

const valueEnum = {
  GENERATING: "生成中",
  GENERATED: "完成",
  EXCEPTION: "异常"
}

const get_color = (status: API.TaskStatusEnum | null | undefined) => {
  if (status === valueEnum.GENERATED)
    return "green";
  if (status === valueEnum.GENERATING)
    return "yellow";
  return "red"
}

const getStatusTag = (record: any) => {
  return (
    <>
      <Tag hidden={record.article_status === null} icon={<FileTextOutlined/>}
           color={get_color(record.article_status)}>文章{record.article_status}</Tag>
      <Tag hidden={record.ref_status === null} icon={<RadiusSettingOutlined/>}
           color={get_color(record.ref_status)}>参考文章{record.ref_status}</Tag>
      <Tag hidden={record.image_status === null} icon={<FileImageOutlined/>}
           color={get_color(record.image_status)}>图片{record.image_status}</Tag>
      <Tag hidden={record.outline_status === null} icon={<OrderedListOutlined/>}
           color={get_color(record.outline_status)}>提纲{record.outline_status}</Tag>
    </>
  )
}

const getPlatformTag = (record: any) => {
  if (record.third_api_key) {
    return (
      <>
        <Tag color={"blue"}>API</Tag>
        <Tag color={"green"}>{record.third_api_key.platform}</Tag>
        <Tag color={"success"}>{record.api_model}</Tag>
      </>
    )
  } else if (record.session) {
    return (
      <>
        <Tag color={"red"}>POE</Tag>
        <Tag color={"orange"}>{record.session.client.name}</Tag>
        <Tag color={"pink"}>{record.session.name}</Tag>
      </>
    )
  }
  // 当平台数据为空时，显示为用户上传
  return (
    <>
      <Tag color={"cyan"}>用户上传</Tag>
    </>
  )
}

const PoeArticle: React.FC = () => {
  const [modelOption, setModelOption] = useState<SelectProps['options']>([]);
  const [poe_session_option, setPoeSessionOptions] = React.useState<SelectProps['options']>([]);
  const [classifyOptions, setClassifyOptions] = React.useState<ClassifyOption[]>([]);
  const [selectedClassify, setSelectedClassify] = React.useState<string | undefined>(undefined);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [syncToDraftModalVisible, setSyncToDraftModalVisible] = useState<boolean>(false);
  const [syncToDraftForm] = Form.useForm();
  const [officialAccounts, setOfficialAccounts] = useState<API.OfficialAccountPublic[]>([]);
  const [selectedOfficialAccount, setSelectedOfficialAccount] = useState<string>('');
  const [thumbList, setThumbList] = useState<ThumbItem[]>([]);

  const onPlatformChanged = async (value: any) => {
    const res: API.ResponseBodyListStr_ = await thirdApiKeyGetModels({platform: value})

    if (res.succeed && res.data) {
      setModelOption(
        res.data.map((item: string) => ({
          label: item,
          value: item,
        }))
      )
    }
  }

  const onPoeClientChanged = async (client_id: any) => {
    const res: API.ResponseBodyPagePoeSessionPublic_ = await poeGetPoeChatSessions({
      client_id: client_id,
      page: 1,
      size: 100,
    });
    if (res.succeed && res.data) {
      setPoeSessionOptions(
        res.data.items.map((item: API.PoeSessionPublic) => ({
          value: item.id,
          label: item.name,
        }))
      )
    }
  }

  // 获取缩略图列表
  const fetchThumbList = async () => {
    try {
      const res = await imageGetArticleThumbs({current: 1, pageSize: 50});
      if (res.succeed && res.data?.items) {
        setThumbList(res.data.items as ThumbItem[]);
      } else {
        message.error('获取缩略图列表失败');
      }
    } catch (error) {
      console.error('获取缩略图列表失败:', error);
      message.error('获取缩略图列表失败');
    }
  };

  // 获取公众号列表
  const fetchOfficialAccounts = async () => {
    try {
      const res = await officialAccountGetOfficialAccountList();
      if (res.succeed && res.data) {
        setOfficialAccounts(res.data);
        if (res.data.length > 0 && res.data[0].id) {
          const defaultAccountId = res.data[0].id;
          setSelectedOfficialAccount(defaultAccountId);
          syncToDraftForm.setFieldsValue({official_account_id: defaultAccountId});
        }
      }
    } catch (error) {
      console.error('获取公众号列表失败:', error);
      message.error('获取公众号列表失败');
    }
  };

  // 获取分类列表
  const fetchClassifyOptions = async () => {
    try {
      const res = await categoryGetAllArticleClassifications({parent_only: false});
      if (res.succeed && res.data) {
        const parentMap = new Map();

        // 创建父级分类映射
        res.data.forEach((item: API.ArticleCategoryPublic) => {
          if (!item.parent) {
            parentMap.set(item.id, {
              label: item.name,
              value: item.id || '',
              children: []
            });
          }
        });

        // 添加子分类到对应的父分类
        res.data.forEach((item: API.ArticleCategoryPublic) => {
          if (item.parent && item.parent.id && parentMap.has(item.parent.id)) {
            parentMap.get(item.parent.id).children.push({
              label: item.name,
              value: item.id || ''
            });
          }
        });

        setClassifyOptions(Array.from(parentMap.values()));
      }
    } catch (error) {
      console.error('获取分类列表失败:', error);
    }
  };

  // 初始化获取分类数据
  useEffect(() => {
    fetchClassifyOptions();
    fetchOfficialAccounts();
    fetchThumbList();
  }, []);

  // 处理分类选择变化
  const handleClassifyChange = (value: string[]) => {
    if (value && value.length > 0) {
      // 使用最后一个值作为实际分类ID
      setSelectedClassify(value[value.length - 1]);
    } else {
      setSelectedClassify(undefined);
    }
  };

  // 处理行选择变化
  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  // 处理公众号选择变化
  const handleOfficialAccountChange = (value: string) => {
    if (value) {
      setSelectedOfficialAccount(value);
      syncToDraftForm.setFieldsValue({official_account_id: value});
    }
  };

  // 打开同步到草稿弹窗
  const openSyncToDraftModal = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择文章');
      return;
    }
    setSyncToDraftModalVisible(true);
  };

  // 处理同步到草稿
  const handleSyncToDraft = async () => {
    try {
      await syncToDraftForm.validateFields();
      const formData = syncToDraftForm.getFieldsValue();

      const params: API.SyncArticleToDraft = {
        article_id: selectedRowKeys as string[],
        official_account_id: formData.official_account_id,
        author: formData.author,
        thumb_media_id: formData.thumb_media_id,
        need_open_comment: formData.need_open_comment,
        only_fans_can_comment: formData.only_fans_can_comment,
        num_of_group:formData.num_of_group,
      };

      const res = await articleSycnArticlesToDraft(params);

      if (res.succeed) {
        message.success('同步到草稿成功');
        setSyncToDraftModalVisible(false);
        // 重置选择的行
        setSelectedRowKeys([]);
      } else {
        message.error(res.errorMessage || '同步到草稿失败');
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const colums: ProColumns<API.ArticleSummaryInfo>[] = [
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      copyable: true,
      ellipsis: true,
      tooltip: '标题过长会自动收缩',
      hideInSearch: true,
    },
    {
      title: "文章生成状态",
      dataIndex: 'article_complete',
      key: 'article_complete',
      render: (_text, record) => getStatusTag(record),
      hideInSearch: true
    },
    {
      title: "平台",
      key: 'platform',
      dataIndex: 'exception',
      render: (_text, record) => getPlatformTag(record),
      hideInSearch: true
    },
    {
      title: "消耗token",
      key: 'msg_prise',
      dataIndex: 'msg_price',
      hideInSearch: true,
    },
    {
      title: "API平台",
      hideInTable: true,
      key: "platform",
      filters: true,
      onFilter: true,
      renderFormItem: () => {
        return (
          <ProFormSelect
            name="platform"
            request={async () => {
              const res: API.ResponseBodyListThirdApiKeyPlatformOption_ = await thirdApiKeyGetThirdApiKeyPlatform();
              return res.succeed && res.data ? res.data : [];
            }}
            onChange={onPlatformChanged}
          />
        )
      },
    },
    {
      title: "API模型",
      hideInTable: true,
      key: "model",
      filters: true,
      onFilter: true,
      renderFormItem: () => {
        return (
          <ProFormSelect
            name={"model"}
            options={modelOption}
          />
        )
      },
    },
    {
      title: "POE账户",
      hideInTable: true,
      key: "poe_client",
      filters: true,
      onFilter: true,
      renderFormItem: () => {
        return (
          <ProFormSelect
            name="poe_client"
            request={async () => {
              const res: API.ResponseBodyListPoeClientPublic_ = await poeGetPoeClientList({});
              if (res.succeed && res.data) {
                return res.data.map((item: API.PoeClientPublic) => ({
                  value: item.id,
                  label: item.name,
                }))
              }
              return [];
            }}
            onChange={onPoeClientChanged}
          />
        )
      },
    },
    {
      title: "POE 会话",
      hideInTable: true,
      key: "poe_session",
      filters: true,
      onFilter: true,
      renderFormItem: () => {
        return (
          <ProFormSelect
            name={"poe_session"}
            options={poe_session_option}
          />
        )
      },
    },
    {
      title: "文章状态",
      key: "article_status",
      hideInTable: true,
      renderFormItem: () => {
        return (
          <ProFormSelect
            valueEnum={valueEnum}>
          </ProFormSelect>
        )
      }
    },
    {
      title: "图片状态",
      key: "image_status",
      hideInTable: true,
      renderFormItem: () => {
        return (
          <ProFormSelect
            valueEnum={valueEnum}>
          </ProFormSelect>
        )
      }
    },
    {
      title: "参考文章获取",
      key: "ref_status",
      hideInTable: true,
      renderFormItem: () => {
        return (
          <ProFormSelect
            valueEnum={valueEnum}>
          </ProFormSelect>
        )
      }
    },
    {
      title: "提纲生成状态",
      key: "outline_status",
      hideInTable: true,
      renderFormItem: () => {
        return (
          <ProFormSelect
            valueEnum={valueEnum}>
          </ProFormSelect>
        )
      }
    },
    {
      title: "文章来源",
      key: "source",
      hideInTable: true,
      renderFormItem: () => {
        return (
          <ProFormSelect
            name="source"
            valueEnum={{
              upload: '用户上传',
              generate: '平台生成'
            }}
          />
        )
      }
    },
    {
      title: "是否同步到草稿",
      key: "sync_to_draft",
      hideInTable: true,
      renderFormItem: () => {
        return (
          <ProFormSelect
            name="sync_to_draft"
            valueEnum={{
              true: '已同步',
              false: '未同步'
            }}
            fieldProps={{
              defaultValue: 'false'
            }}
          />
        )
      },
      initialValue: 'false',
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      render: (_, record) => {
        // 获取分类名称
        let categoryName = '未知分类';

        if (record.category) {
          categoryName = record.category.name;
        }

        return record.category ? (
          <Tag icon={<TagsOutlined/>} color="processing">
            {categoryName}
          </Tag>
        ) : null;
      },
      hideInSearch: true
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      valueType: 'dateRange',
      key: 'created_at',
      hideInTable: true,
      search: {
        transform: (value) => {
          return {
            startTime: value[0],
            endTime: value[1],
          };
        },
      },
    },
    {
      title: "分类筛选",
      hideInTable: true,
      key: "category_id",
      dataIndex: 'category_id',
      renderFormItem: () => {
        return (
          <Cascader
            options={classifyOptions}
            expandTrigger="hover"
            displayRender={(labels) => labels.join(' / ')}
            placeholder="请选择文章分类"
            onChange={(value) => handleClassifyChange(value as string[])}
            allowClear
          />
        );
      }
    },

    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      render: (_text, record) => [
        <Tooltip title={"编辑"} key={"edit"}>
          <Link to={"/edit/" + record.id} target={'_blank'}>
            <Button type={'primary'} shape={'circle'} icon={<EditOutlined/>}></Button>
          </Link>
        </Tooltip>,
        <Popconfirm
          title="确认删除文章"
          description={"确认删除文章：" + record.title + "?"}
          okText="确认"
          cancelText="取消"
          key={'delete'}
          onConfirm={async () => {
            if (record.id) {
              const msg: API.DeleteResponse = await articleDeletePoeArticle({article_id: record.id});
              if (msg.succeed) {
                message.info(msg.message || '删除成功');
              } else {
                message.error((msg.errorCode || '') + (msg.errorMessage || '删除失败'));
              }
            }
          }}
        >
          <Tooltip title={"删除"}>
            <Button danger shape={'circle'} icon={<DeleteOutlined/>}></Button>
          </Tooltip>
        </Popconfirm>,
      ],
    },
  ]

  // 渲染同步到草稿表单
  const renderSyncToDraftForm = () => {
    return (
      <Form
        form={syncToDraftForm}
        layout="vertical"
        initialValues={{need_open_comment: true, only_fans_can_comment: false, num_of_group: 1}}
      >
        <Form.Item
          name="official_account_id"
          label="公众号"
          rules={[{required: true, message: '请选择公众号'}]}
        >
          <Select
            placeholder="请选择公众号"
            onChange={handleOfficialAccountChange}
          >
            {officialAccounts.map(account => (
              <Select.Option key={account.id} value={account.id}>
                {account.nick_name || account.appid || '未命名公众号'}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="author"
          label="作者"
          rules={[{required: true, message: '请输入作者名称'}]}
        >
          <Input placeholder="请输入作者名称"/>
        </Form.Item>

        <Form.Item
          name="thumb_media_id"
          label="封面图片"
          rules={[{required: true, message: '请选择或上传封面图片'}]}
        >
          <ThumbSelector
            officialAccountId={selectedOfficialAccount}
            categoryId={selectedClassify}
          />
        </Form.Item>

        <Form.Item name="need_open_comment" valuePropName="checked">
          <Checkbox>允许评论</Checkbox>
        </Form.Item>

        <Form.Item name="only_fans_can_comment" valuePropName="checked">
          <Checkbox>仅粉丝可评论</Checkbox>
        </Form.Item>

        <Form.Item
          name="num_of_group"
          label="将多篇文字合并为一个草稿"
        >
          <InputNumber min={1} max={8}/>
        </Form.Item>
      </Form>
    );
  };

  return (
    <>
      <Space style={{marginBottom: 16}}>
        <Button
          type="primary"
          onClick={openSyncToDraftModal}
          disabled={selectedRowKeys.length === 0}
          icon={<SyncOutlined/>}
        >
          同步到草稿
        </Button>
        <span style={{marginLeft: 8}}>
          {selectedRowKeys.length > 0 ? `已选择 ${selectedRowKeys.length} 项` : ''}
        </span>
      </Space>

      <ProTable<any>
        rowKey="id"
        rowSelection={rowSelection}
        columns={colums}
        request={async (params, sort, filter) => {
          // 将级联选择器的选中值添加到查询参数中
          let requestParams = {...params};
          if (selectedClassify) {
            requestParams.category_id = selectedClassify;
          }

          // 修复API调用前的值转换
          if (params.article_status) {
            params.article_status = valueEnum[params.article_status as keyof typeof valueEnum]
          }
          if (params.ref_status) {
            params.ref_status = valueEnum[params.ref_status as keyof typeof valueEnum]
          }
          if (params.image_status) {
            params.image_status = valueEnum[params.image_status as keyof typeof valueEnum]
          }
          if (params.outline_status) {
            params.outline_status = valueEnum[params.outline_status as keyof typeof valueEnum]
          }
          // 转换文章来源值
          if (params.source) {
            // 不需要转换，直接传递upload或generate值即可
          }

          // 处理sync_to_draft参数
          if (params.sync_to_draft !== undefined) {
            if (params.sync_to_draft === 'true') {
              params.sync_to_draft = true;
            } else if (params.sync_to_draft === 'false') {
              params.sync_to_draft = false;
            }
          } else {
            // 默认只显示未同步的文章
            params.sync_to_draft = false;
          }

          // 执行查询
          const res = await articleGetArticlesSummary(requestParams);
          if (res.succeed) {
            // 返回前，为每个记录处理source属性
            const items = res.data?.items || [];
            const processedItems = items.map(item => {
              let source = 'generate'; // 默认为平台生成
              if (!item.third_api_key && !item.session) {
                source = 'upload'; // 如果没有平台信息，则为用户上传
              }
              return {...item, source} as any;
            });

            return {
              data: processedItems,
              success: true,
              total: res.data?.total || 0,
            }
          }
          return {
            data: [],
            success: false,
            total: 0,
          }
        }}
      />

      <Modal
        title="同步到草稿"
        open={syncToDraftModalVisible}
        onOk={handleSyncToDraft}
        onCancel={() => setSyncToDraftModalVisible(false)}
        width={700}
      >
        {renderSyncToDraftForm()}
      </Modal>
    </>
  );
};

export default PoeArticle;
